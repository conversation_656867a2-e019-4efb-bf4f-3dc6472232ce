import { type RootState } from '../store';
import { type Product, type User, type Order } from '../types';
import allProductsData from '../assets/products/all_products.json';


const LOCAL_STORAGE_KEY = 'reduxState';

export const loadState = (): RootState | undefined => {
  try {
    const serializedState = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (serializedState === null) {
      return undefined;
    }
    const state = JSON.parse(serializedState);

    // Initialize products if not present in localStorage
    if (!state.product || state.product.products.length === 0) {
      state.product = {
        ...state.product,
        products: allProductsData as Product[],
      };
    }
    // Initialize users if not present in localStorage
    if (!state.user || state.user.users.length === 0) {
      state.user = {
        ...state.user,
        users: [],
      };
    }
    // Initialize cart if not present in localStorage
    if (!state.cart || state.cart.items.length === 0) {
      state.cart = {
        ...state.cart,
        items: [],
      };
    }
    // Initialize orders if not present in localStorage
    if (!state.order || state.order.orders.length === 0) {
      state.order = {
        ...state.order,
        orders: [],
      };
    }

    return state;
  } catch (err) {
    console.error("Error loading state from localStorage:", err);
    return undefined;
  }
};

export const initializeData = (): void => {
  try {
    const state = loadState();
    if (!state || !state?.product || state?.product.products.length === 0) {
      const initialState: Partial<RootState> = {
        products: {
          products: allProducts?.products,
        },
        cart: {
          items: [],
        },
        user: {
          currentUser: null,
        },
      };
      saveState(initialState);
    }
  } catch (err) {
    console.error('Could not initialize data in localStorage:', err);
  }
};

export const saveState = (state: RootState) => {
  try {
    const serializedState = JSON.stringify(state);
    localStorage.setItem(LOCAL_STORAGE_KEY, serializedState);
  } catch (err) {
    console.error("Error saving state to localStorage:", err);
  }
};

export const setCurrentUser = (user: User) => {
  try {
    const state = loadState();
    if (state) {
      const newState: RootState = {
        ...state,
        user: {
          ...state.user,
          currentUser: user,
        },
      };
      saveState(newState);
    }
  } catch (err) {
    console.error('Error setting current user in localStorage:', err);
  }
};

export const clearCurrentUser = () => {
  try {
    const state = loadState();
    if (state) {
      state.user.currentUser = null;
      saveState(state);
    }
  } catch (err) {
    console.error('Error clearing current user from localStorage:', err);
  }
};

export const getCurrentUser = (): User | null => {
  try {
    const state = loadState();
    return state?.user?.currentUser || null;
  } catch (err) {
    console.error("Error getting current user from localStorage:", err);
    return null;
  }
};

export const getOrders = (): Order[] => {
  try {
    const state = loadState();
    return state?.order?.orders || [];
  } catch (err) {
    console.error("Error getting orders from localStorage:", err);
    return [];
  }

  
};
